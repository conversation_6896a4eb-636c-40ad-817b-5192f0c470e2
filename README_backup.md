# Proyecto-Cafe

Aplicación Streamlit para gestionar el emprendimiento de Café de forma separada de "Finanzas Familiares".

## Características
- Ventas, Compras, Clientes, Cuentas por Cobrar, Inventario
- Base de datos propia: `cafe.db` (SQLite)
- Opción de sincronizar/respaldar la BD en Google Drive (opcional)
- Script de migración para importar los datos de Café desde `finanzas_familiares.db`

## Requisitos
- Python 3.10+
- Entorno virtual (recomendado)

## Instalación rápida

```bash
cd Proyecto-Cafe
python -m venv .venv
source .venv/bin/activate  # En Windows: .venv\\Scripts\\activate
pip install -r requirements.txt
```

## Ejecutar la app

```bash
streamlit run main.py
```

## Configuración de Google Drive (opcional)
- Crea un proyecto en Google Cloud y una credencial de "Service Account"
- Comparte la carpeta de Drive donde guardarás la BD con el correo de la Service Account (rol: Editor)
- Obtén:
  - JSON de credenciales (contenido completo)
  - ID de la carpeta de Drive (FOLDER_ID)
- En producción (Streamlit Cloud), guarda en Secrets:

```toml
GDRIVE_SERVICE_ACCOUNT_JSON = "{ ... json ... }"
GDRIVE_FOLDER_ID = "xxxxxxxxxxxxxxxx"
```

La app intentará descargar `cafe.db` desde Drive al iniciar (si no existe) y subirá la BD después de registrar ventas/compras/cobros. También tendrás un botón "Sincronizar ahora".

## Migración de datos desde Finanzas Familiares

```bash
# Con el entorno activado
python -m tools.migrar_desde_finanzas --origen ../finanzasfamilia/finanzas_familiares.db
```

- Copia tablas: clientes, proveedores, cafe_ventas, cafe_compras, cafe_cxc, inventario_items
- Copia transacciones SOLO de Café (ventas/costos/inventario/CxC) a `cafe.db`
- No modifica el archivo de origen

## Estructura de carpetas
```
Proyecto-Cafe/
  cafe_app/
    database.py
    remote_storage_gdrive.py
  tools/
    migrar_desde_finanzas.py
  main.py
  requirements.txt
  README.md
  manual-usuario.md
```

## Licencia
Uso interno. Ajusta según tus necesidades.
