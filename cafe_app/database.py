import sqlite3
from typing import Optional
import pandas as pd

DB_PATH = "cafe.db"


def init_db(db_path: str = DB_PATH):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    # Transacciones (comunes para reportes)
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS transacciones (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fecha TEXT,
            tipo TEXT,
            categoria TEXT,
            subcategoria TEXT,
            subsubcategoria TEXT,
            monto REAL,
            unidades REAL,
            medida TEXT,
            lugar TEXT,
            descripcion TEXT,
            observaciones TEXT
        )
        """
    )
    # Clientes
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombres TEXT NOT NULL,
            apellidos TEXT NOT NULL,
            celular TEXT NOT NULL,
            correo TEXT,
            direccion TEXT,
            cumple TEXT,
            notas TEXT,
            activo INTEGER DEFAULT 1
        )
        """
    )
    c.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_clientes_celular ON clientes(celular)')

    # Migración: agregar campo notas si no existe
    try:
        c.execute("ALTER TABLE clientes ADD COLUMN notas TEXT")
    except sqlite3.OperationalError:
        # La columna ya existe
        pass
    # Proveedores
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS proveedores (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre TEXT NOT NULL,
            celular TEXT,
            correo TEXT,
            direccion TEXT
        )
        """
    )
    # Catálogo inventario
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS inventario_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nombre TEXT NOT NULL UNIQUE,
            medida TEXT,
            activo INTEGER DEFAULT 1
        )
        """
    )
    # Ventas
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS cafe_ventas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fecha TEXT NOT NULL,
            cliente_id INTEGER,
            item TEXT,
            unidades REAL,
            medida TEXT,
            precio_total REAL,
            costo_unitario REAL,
            observaciones TEXT,
            forma_pago TEXT,
            metodo_pago TEXT,
            vencimiento TEXT,
            FOREIGN KEY(cliente_id) REFERENCES clientes(id)
        )
        """
    )
    # Compras
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS cafe_compras (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fecha TEXT NOT NULL,
            proveedor_id INTEGER,
            item TEXT,
            unidades REAL,
            medida TEXT,
            costo_unitario REAL,
            costo_total REAL,
            observaciones TEXT,
            FOREIGN KEY(proveedor_id) REFERENCES proveedores(id)
        )
        """
    )
    # CxC
    c.execute(
        """
        CREATE TABLE IF NOT EXISTS cafe_cxc (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fecha TEXT NOT NULL,
            cliente_id INTEGER,
            descripcion TEXT,
            monto REAL,
            vencimiento TEXT,
            pagado INTEGER DEFAULT 0,
            fecha_pago TEXT,
            metodo TEXT,
            FOREIGN KEY(cliente_id) REFERENCES clientes(id)
        )
        """
    )
    conn.commit()
    conn.close()


# Utilidades transacciones

def add_transaction(fecha, tipo, categoria, subcategoria, subsubcategoria, monto, unidades, medida, lugar, descripcion, observaciones=None, db_path: str = DB_PATH):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute(
        """
        INSERT INTO transacciones (fecha, tipo, categoria, subcategoria, subsubcategoria, monto, unidades, medida, lugar, descripcion, observaciones)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
        (fecha, tipo, categoria, subcategoria, subsubcategoria, monto, unidades, medida, lugar, descripcion, observaciones),
    )
    conn.commit()
    conn.close()


def obtener_datos_financieros(db_path: str = DB_PATH) -> pd.DataFrame:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT * FROM transacciones")
    datos = c.fetchall()
    columnas = [desc[0] for desc in c.description]
    conn.close()
    return pd.DataFrame(datos, columns=columnas)


# Clientes

def upsert_cliente(nombres: str, apellidos: str, celular: str, correo: Optional[str] = None, direccion: Optional[str] = None, cumple: Optional[str] = None, notas: Optional[str] = None, db_path: str = DB_PATH) -> int:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT id FROM clientes WHERE celular=?", (celular,))
    row = c.fetchone()
    if row:
        cid = row[0]
        c.execute("UPDATE clientes SET nombres=?, apellidos=?, correo=?, direccion=?, cumple=?, notas=?, activo=1 WHERE id=?",
                  (nombres, apellidos, correo, direccion, cumple, notas, cid))
    else:
        c.execute("INSERT INTO clientes (nombres, apellidos, celular, correo, direccion, cumple, notas) VALUES (?,?,?,?,?,?,?)",
                  (nombres, apellidos, celular, correo, direccion, cumple, notas))
        cid = c.lastrowid
    conn.commit()
    conn.close()
    return cid


def search_clientes(query: str, include_inactive: bool = False, db_path: str = DB_PATH) -> pd.DataFrame:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    q = f"%{query.lower()}%"
    if include_inactive:
        c.execute(
            """
            SELECT id, nombres, apellidos, celular, correo, direccion, cumple, notas, activo
            FROM clientes
            WHERE (lower(nombres) LIKE ? OR lower(apellidos) LIKE ? OR celular LIKE ?)
            ORDER BY apellidos, nombres
            """,
            (q, q, q),
        )
    else:
        c.execute(
            """
            SELECT id, nombres, apellidos, celular, correo, direccion, cumple, notas, activo
            FROM clientes
            WHERE activo=1 AND (lower(nombres) LIKE ? OR lower(apellidos) LIKE ? OR celular LIKE ?)
            ORDER BY apellidos, nombres
            """,
            (q, q, q),
        )
    rows = c.fetchall()
    conn.close()
    return pd.DataFrame(rows, columns=["id", "nombres", "apellidos", "celular", "correo", "direccion", "cumple", "notas", "activo"])


def update_cliente_by_id(cid: int, nombres: str, apellidos: str, celular: str, correo: Optional[str] = None, direccion: Optional[str] = None, cumple: Optional[str] = None, notas: Optional[str] = None, db_path: str = DB_PATH):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute(
        "UPDATE clientes SET nombres=?, apellidos=?, celular=?, correo=?, direccion=?, cumple=?, notas=? WHERE id=?",
        (nombres, apellidos, celular, correo, direccion, cumple, notas, cid),
    )
    conn.commit()
    conn.close()


def set_cliente_activo(cid: int, activo: bool, db_path: str = DB_PATH):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("UPDATE clientes SET activo=? WHERE id=?", (1 if activo else 0, cid))
    conn.commit()
    conn.close()


def get_cliente_by_id(cid: int, db_path: str = DB_PATH) -> Optional[dict]:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT id, nombres, apellidos, celular, correo, direccion, cumple, notas, activo FROM clientes WHERE id=?", (cid,))
    row = c.fetchone()
    conn.close()
    if not row:
        return None
    keys = ["id", "nombres", "apellidos", "celular", "correo", "direccion", "cumple", "notas", "activo"]
    return dict(zip(keys, row))


# Inventario y compras/ventas

def upsert_inventario_item(nombre: str, medida: Optional[str] = None, activo: bool = True, db_path: str = DB_PATH) -> int:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT id FROM inventario_items WHERE nombre=?", (nombre,))
    row = c.fetchone()
    if row:
        c.execute("UPDATE inventario_items SET medida=COALESCE(?, medida), activo=? WHERE id=?", (medida, 1 if activo else 0, row[0]))
        iid = row[0]
    else:
        c.execute("INSERT INTO inventario_items (nombre, medida, activo) VALUES (?,?,?)", (nombre, medida, 1 if activo else 0))
        iid = c.lastrowid
    conn.commit()
    conn.close()
    return iid


def get_inventario_items(include_inactive: bool = False, db_path: str = DB_PATH) -> pd.DataFrame:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    if include_inactive:
        c.execute("SELECT id, nombre, medida, activo FROM inventario_items ORDER BY nombre")
    else:
        c.execute("SELECT id, nombre, medida, activo FROM inventario_items WHERE activo=1 ORDER BY nombre")
    rows = c.fetchall()
    conn.close()
    return pd.DataFrame(rows, columns=["id", "nombre", "medida", "activo"])


def add_cafe_compra(fecha: str, proveedor_id: Optional[int], item: str, unidades: float, medida: str, costo_unitario: float, observaciones: Optional[str] = None, db_path: str = DB_PATH):
    costo_total = float(costo_unitario) * float(unidades)
    try:
        upsert_inventario_item(item, medida, True, db_path)
    except Exception:
        pass
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute(
        """
        INSERT INTO cafe_compras (fecha, proveedor_id, item, unidades, medida, costo_unitario, costo_total, observaciones)
        VALUES (?,?,?,?,?,?,?,?)
        """,
        (fecha, proveedor_id, item, unidades, medida, costo_unitario, costo_total, observaciones),
    )
    conn.commit()
    conn.close()
    add_transaction(fecha, "Activos", "Inventarios", item, None, costo_total, float(unidades), medida, None, f"Entrada inventario: {item}", observaciones, db_path)


def get_ultimo_costo_compra(item: str, db_path: str = DB_PATH) -> Optional[float]:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT costo_unitario FROM cafe_compras WHERE item=? ORDER BY fecha DESC, id DESC LIMIT 1", (item,))
    row = c.fetchone()
    conn.close()
    if not row:
        return None
    try:
        return float(row[0]) if row[0] is not None else None
    except Exception:
        return None


def add_cafe_venta(
    fecha: str,
    cliente_id: Optional[int],
    item: str,
    unidades: float,
    medida: str,
    precio_total: float,
    costo_unitario: float,
    observaciones: Optional[str] = None,
    forma_pago: Optional[str] = None,
    metodo_pago: Optional[str] = None,
    vencimiento: Optional[str] = None,
    db_path: str = DB_PATH,
):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute(
        """
        INSERT INTO cafe_ventas (fecha, cliente_id, item, unidades, medida, precio_total, costo_unitario, observaciones, forma_pago, metodo_pago, vencimiento)
        VALUES (?,?,?,?,?,?,?,?,?,?,?)
        """,
        (fecha, cliente_id, item, unidades, medida, precio_total, costo_unitario, observaciones, forma_pago, metodo_pago, vencimiento),
    )
    conn.commit()
    conn.close()
    add_transaction(fecha, "Ingreso", "Ventas", None, None, float(precio_total), float(unidades), medida, None, f"Venta café: {item}", observaciones, db_path)
    costo_total = float(costo_unitario) * float(unidades)
    add_transaction(fecha, "Gasto", "Negocio", "Costo de ventas", None, costo_total, float(unidades), medida, None, f"Costo café: {item}", observaciones, db_path)
    add_transaction(fecha, "Activos", "Inventarios", item, None, -costo_total, -float(unidades), medida, None, f"Salida inventario café: {item}", observaciones, db_path)

    if forma_pago and forma_pago.lower() == "crédito":
        desc = f"Venta crédito café: {item} ({unidades} {medida})"
        add_cxc(fecha, cliente_id or 0, desc, float(precio_total), vencimiento, db_path)
    else:
        if metodo_pago:
            add_transaction(fecha, "Activos", "Efectivo y Bancos", metodo_pago, None, float(precio_total), None, None, None, f"Cobro contado: {metodo_pago}", None, db_path)


# Proveedores, CxC

def upsert_proveedor(nombre: str, celular: Optional[str] = None, correo: Optional[str] = None, direccion: Optional[str] = None, db_path: str = DB_PATH) -> int:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT id FROM proveedores WHERE nombre=?", (nombre,))
    row = c.fetchone()
    if row:
        pid = row[0]
        c.execute("UPDATE proveedores SET celular=?, correo=?, direccion=? WHERE id=?", (celular, correo, direccion, pid))
    else:
        c.execute("INSERT INTO proveedores (nombre, celular, correo, direccion) VALUES (?,?,?,?)", (nombre, celular, correo, direccion))
        pid = c.lastrowid
    conn.commit()
    conn.close()
    return pid


def add_cxc(fecha: str, cliente_id: int, descripcion: str, monto: float, vencimiento: Optional[str] = None, db_path: str = DB_PATH) -> int:
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute(
        "INSERT INTO cafe_cxc (fecha, cliente_id, descripcion, monto, vencimiento, pagado) VALUES (?,?,?,?,?,0)",
        (fecha, cliente_id, descripcion, monto, vencimiento),
    )
    cid = c.lastrowid
    conn.commit()
    conn.close()
    add_transaction(fecha, "Activos", "Cuentas por cobrar", descripcion, None, float(monto), None, None, None, f"CxC: {descripcion}", None, db_path)
    return cid


def registrar_cobro_cxc(cxc_id: int, fecha_pago: str, metodo: Optional[str] = None, db_path: str = DB_PATH):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute("SELECT cliente_id, descripcion, monto FROM cafe_cxc WHERE id=? AND pagado=0", (cxc_id,))
    row = c.fetchone()
    if not row:
        conn.close()
        return False
    cliente_id, descripcion, monto = row
    c.execute("UPDATE cafe_cxc SET pagado=1, fecha_pago=?, metodo=? WHERE id=?", (fecha_pago, metodo, cxc_id))
    conn.commit()
    conn.close()
    add_transaction(fecha_pago, "Activos", "Cuentas por cobrar", descripcion, None, -float(monto), None, None, None, f"Cobro CxC: {descripcion}", None, db_path)
    add_transaction(fecha_pago, "Activos", "Efectivo y Bancos", metodo, None, float(monto), None, None, None, f"Ingreso por cobro CxC: {descripcion}", None, db_path)
    return True

