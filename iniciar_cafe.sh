#!/bin/bash

# Script para iniciar la aplicación del Café
# Ubicación: /home/<USER>/Proyecto-Cafe

echo "==================================="
echo "   INICIANDO APLICACIÓN DEL CAFÉ   "
echo "==================================="

# Cambiar al directorio del proyecto
cd /home/<USER>/Proyecto-Cafe

# Verificar que el entorno virtual existe
if [ ! -d ".venv" ]; then
    echo "❌ Error: No se encontró el entorno virtual."
    echo "Creando entorno virtual..."
    python -m venv .venv
    source .venv/bin/activate
    pip install -r requirements.txt
else
    echo "✅ Entorno virtual encontrado."
fi

# Activar el entorno virtual
echo "🔄 Activando entorno virtual..."
source .venv/bin/activate

# Verificar que las dependencias están instaladas
echo "🔍 Verificando dependencias..."
if ! python -c "import streamlit" 2>/dev/null; then
    echo "📦 Instalando dependencias..."
    pip install -r requirements.txt
fi

# Verificar que la base de datos existe
if [ ! -f "cafe.db" ]; then
    echo "⚠️  Advertencia: No se encontró la base de datos cafe.db"
    echo "La aplicación creará una nueva base de datos al iniciarse."
fi

echo "🚀 Iniciando servidor Streamlit..."
echo "📱 La aplicación estará disponible en: http://localhost:8501"
echo "⏹️  Para detener la aplicación, presiona Ctrl+C"
echo ""

# Iniciar la aplicación
streamlit run main.py --server.headless false --server.port 8501
