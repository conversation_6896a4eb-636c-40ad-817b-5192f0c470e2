# Manual de usuario - Proyecto Café

## Secciones
- Clientes: crear/editar/inactivar; búsqueda rápida.
- Ventas: contado o crédito; para crédito selecciona cliente y vencimiento.
- Compras: registra compras a proveedores, actualiza inventario y costos.
- Inventario: catálogo de ítems y medidas.
- Reportes: tabla simple de transacciones (por ahora). Próximamente KPIs.

## Flujo típico
1. Registra compras (actualiza inventario y costo de referencia)
2. Registra ventas: 
   - Contado: selecciona método (Efectivo, Nequi, etc.)
   - Crédito: selecciona cliente y vencimiento; genera CxC
3. Cobros de CxC: en una iteración próxima se agrega la UI (ya existe en DB)

## Respaldo en Google Drive (opcional)
- Define variables de entorno o Secrets:
  - GDRIVE_SERVICE_ACCOUNT_JSON = "{...}"
  - GDRIVE_FOLDER_ID = "id_de_carpeta"
- La app intenta descargar `cafe.db` al iniciar si no existe y ofrece botón para subir.

## Migración de datos
- Usa: `python -m tools.migrar_desde_finanzas --origen ../finanzasfamilia/finanzas_familiares.db`.
- No modifica el origen.

## Atajos
- Archivo de BD: `cafe.db` en la raíz del proyecto.
- Para mover el proyecto a /home/<USER>/Proyectos/Proyecto-Cafe, copia la carpeta y conserva `cafe.db`.

